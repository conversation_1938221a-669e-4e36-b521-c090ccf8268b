'use client';

import { useAuth } from '@/hooks/useAuth';

export default function Home() {
  const { isAuthenticated, loading: isLoading, error } = useAuth();


  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      {isLoading ? (
        <>
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <p className="mt-4 text-gray-500">Checking authentication status...</p>
        </>
      ) : isAuthenticated ? (
        <>
          <p className="mt-4 text-gray-500">Welcome back!</p>
        </>
      ) : (
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold mb-6">Welcome to the Application</h1>
          
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}
          
          <p className="mb-6">
            Please sign in to access the application.
          </p>
          
        </div>
      )}
    </div>
  );
}
