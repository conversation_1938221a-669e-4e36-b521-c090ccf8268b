import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "@/lib/providers";
import TokenHandler from "@/components/common/TokenHandler";
import { Suspense } from "react";
import { PROJECT_DESCRIPTION, PROJECT_NAME } from "@/constants/config";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: PROJECT_NAME,
  description: PROJECT_DESCRIPTION,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>
          {/* Suspense is required because <PERSON><PERSON><PERSON><PERSON><PERSON> uses useSearchParams */}
          <Suspense fallback={<div>Loading...</div>}>
            <TokenHandler />
          </Suspense>
          {children}
        </Providers>
      </body>
    </html>
  );
}
