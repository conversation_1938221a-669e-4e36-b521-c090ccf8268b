import { Store } from '@reduxjs/toolkit';
import { clearAuth } from '@/store/slices/authSlice';
import httpService from '@/services/http';

export const setupHttpService = (store: Store) => {
  httpService.setup({
    tokenProvider: () => store.getState().auth.token,
    onAuthError: () => {
      store.dispatch(clearAuth());
      // Redirect to home page for re-authentication
      window.location.href = '/';
    },
  });
};
