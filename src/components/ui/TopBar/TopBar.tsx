"use client";

import { useEffect, useState } from "react";
import SearchBar from "@/components/ui/SearchBar";

export default function TopBar() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  return (
    <div className="fixed top-4 left-0 right-0 z-50 pl-0 lg:pl-[200px] bg-white">
      <div className="flex items-center justify-start px-0 py-3 h-16">

        {/* Search Bar Section */}
        <div className="flex-1 max-w-2xl ml-10">
          {!isMobile && <SearchBar />}
        </div>

      </div>
    </div>
  );
}