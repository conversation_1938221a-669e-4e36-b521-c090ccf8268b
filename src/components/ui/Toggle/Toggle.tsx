'use client';

import { useState } from 'react';

interface ToggleProps {
    id?: string;
    checked?: boolean;
    onChange?: (checked: boolean) => void;
    disabled?: boolean;
    label?: string;
    className?: string;
    size?: 'sm' | 'md' | 'lg';
}

export const Toggle = ({
    id,
    checked = false,
    onChange,
    disabled = false,
    label,
    className = '',
    size = 'md'
}: ToggleProps) => {
    const [internalChecked, setInternalChecked] = useState(checked);
    
    const isChecked = onChange ? checked : internalChecked;
    
    const handleToggle = () => {
        if (disabled) return;
        
        if (onChange) {
            onChange(!checked);
        } else {
            setInternalChecked(!internalChecked);
        }
    };

    const sizeClasses = {
        sm: 'w-8 h-4',
        md: 'w-11 h-6',
        lg: 'w-14 h-8'
    };

    const thumbSizeClasses = {
        sm: 'w-3 h-3',
        md: 'w-5 h-5',
        lg: 'w-6 h-6'
    };

    const translateClasses = {
        sm: isChecked ? 'translate-x-4' : 'translate-x-0.5',
        md: isChecked ? 'translate-x-5' : 'translate-x-0.5',
        lg: isChecked ? 'translate-x-6' : 'translate-x-1'
    };

    return (
        <div className={`flex items-center ${className}`}>
            <div className="relative">
                <button
                    type="button"
                    role="switch"
                    aria-checked={isChecked}
                    id={id}
                    onClick={handleToggle}
                    disabled={disabled}
                    className={`
                        ${sizeClasses[size]}
                        relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out
                        focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2
                        ${isChecked 
                            ? 'bg-teal-500' 
                            : 'bg-gray-200'
                        }
                        ${disabled 
                            ? 'opacity-50 cursor-not-allowed' 
                            : 'cursor-pointer'
                        }
                    `}
                >
                    <span
                        className={`
                            ${thumbSizeClasses[size]}
                            ${translateClasses[size]}
                            inline-block rounded-full bg-white shadow transform transition-transform duration-200 ease-in-out
                        `}
                    />
                </button>
            </div>
            {label && (
                <label
                    htmlFor={id}
                    className={`ml-3 text-sm font-medium text-gray-700 ${
                        disabled ? 'text-gray-400' : 'cursor-pointer'
                    }`}
                    onClick={!disabled ? handleToggle : undefined}
                >
                    {label}
                </label>
            )}
        </div>
    );
};

export default Toggle;