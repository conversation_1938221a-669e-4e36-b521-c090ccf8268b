import { Search } from "lucide-react"
import { useDispatch, useSelector } from "react-redux"
import { useState, useRef, useEffect } from "react"
import { useRouter, usePathname, useSearchParams } from "next/navigation"
import { RootState } from "@/store/types"
import {
  setCurrentQuery,
  addToSearchHistory,
  setSuggestions,
  setShowSuggestions,
  addToRecentSearches
} from "@/store/slices/searchSlice"

export default function SearchBar() {
  const [isFocused, setIsFocused] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()
  const dispatch = useDispatch()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Get state from Redux store
  const {
    currentQuery,
    suggestions,
    showSuggestions,
    searchHistory
  } = useSelector((state: RootState) => state.search)

  const [query, setQuery] = useState(currentQuery)

  useEffect(() => {
    dispatch(setCurrentQuery(query))
  }, [query, dispatch])

  const handleSubmit = (searchQuery: string) => {
    dispatch(setShowSuggestions(false))
    if (searchQuery.trim()) {
      // Add to search history in Redux store
      dispatch(addToSearchHistory(searchQuery))
      dispatch(addToRecentSearches(searchQuery))

      // Also maintain localStorage for backward compatibility
      const storedQueries: string[] = JSON.parse(
        localStorage.getItem("searchQueries") || "[]"
      )
      if (!storedQueries.includes(searchQuery)) {
        storedQueries.push(searchQuery)
        localStorage.setItem("searchQueries", JSON.stringify(storedQueries))
      }

      router.push(`/search?query=${encodeURIComponent(searchQuery)}`)
    }
  }

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    handleSubmit(query)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion)
    handleSubmit(suggestion)
    dispatch(setShowSuggestions(false))
  }

  const handleInputFocus = () => {
    setIsFocused(true)
  }

  const handleInputBlur = () => {
    setIsFocused(false)
  }

  useEffect(() => {
    if (query && isFocused) {
      // Get suggestions from localStorage and Redux store
      const storedQueries: string[] = JSON.parse(
        localStorage.getItem("searchQueries") || "[]"
      )

      // Combine localStorage and Redux search history
      const allQueries = [...new Set([...storedQueries, ...searchHistory])]

      const filteredSuggestions = allQueries
        .filter((item) => item.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 5)

      dispatch(setSuggestions(filteredSuggestions))
      dispatch(setShowSuggestions(true))
    } else {
      dispatch(setSuggestions([]))
      dispatch(setShowSuggestions(false))
    }
  }, [query, isFocused, searchHistory, dispatch])

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        dispatch(setShowSuggestions(false))
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [dispatch])

  useEffect(() => {
    if (pathname === "/search") {
      const queryParam = searchParams.get("query") || "";
      setQuery(queryParam);
    }
    dispatch(setShowSuggestions(false));
  }, [searchParams, pathname, dispatch]);

  useEffect(() => {
    if (pathname !== "/search") {
      setQuery(""); // Clear input when leaving search page
    }
    dispatch(setShowSuggestions(false));
  }, [pathname, dispatch]);

  return (
    <div 
      className={`relative w-full top-0 transition-all duration-300 z-5 h-12 `}
      ref={containerRef}
    >
      <div 
        className={`flex justify-center items-center z-5 h-full`}
      >
        <div className="w-full">
          <form onSubmit={handleFormSubmit}>
            <div className="flex items-center w-full rounded-full bg-[#f9f9f9] border border-gray-200 shadow-md px-4 py-3">
              <Search className="h-5 w-5 text-gray-400 mr-2" />
              <input
                ref={inputRef}
                type="text"
                placeholder="Search for Data Asset, Projects and Documents..."
                className="flex-1 bg-transparent border-none outline-none text-gray-600 placeholder-gray-400 text-sm"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
              />
            </div>
          </form>

          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute w-full mt-2 bg-white rounded-lg border border-gray-200 shadow-lg overflow-hidden">
              <ul className="py-1">
                {suggestions.map((suggestion, index) => (
                  <li
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="flex items-center px-4 py-2 hover:bg-gray-50 cursor-pointer"
                  >
                    <Search className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-700">{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}