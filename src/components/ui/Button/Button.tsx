"use client";

type ButtonProps = {
    children: React.ReactNode;
    onClick?: () => void;
    loading?: boolean;
    title: string;
    loadingText?: string;
    height?: number;
    width?: number;
    variant?: 'primary' | 'secondary' | 'tertiary' | 'ghost';
    className?: string;
};

export const Button = ({ 
    children, 
    onClick, 
    loading, 
    title, 
    loadingText, 
    height, 
    width,
    variant = 'primary',
    className = ''
}: ButtonProps) => {
    const baseClasses = `transition-colors duration-200 py-2 rounded font-medium disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2`;
    
    const variantClasses = {
        primary: 'bg-[#00B2A1] text-white hover:bg-[#009688]',
        secondary: 'bg-white text-teal-500 border-2 border-teal-500 hover:bg-teal-500 hover:text-white',
        tertiary: 'bg-white text-gray-500 border-2 border-gray-500 hover:bg-gray-500 hover:text-white',
        ghost: 'bg-transparent text-teal-500 hover:bg-teal-50'
    };

    const sizeClasses = width ? `w-[${width}px]` : 'w-full';
    const heightStyle = { height: `${height || 40}px` };

    return (
        <button
            type="button"
            onClick={onClick}
            className={`${sizeClasses} ${baseClasses} ${variantClasses[variant]} ${className}`}
            style={width ? { width: `${width}px`, ...heightStyle } : heightStyle}
            disabled={loading}
        >
            {loading ? loadingText : title}
            {children}
        </button>
    );
};