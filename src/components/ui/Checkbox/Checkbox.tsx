import React from "react";

interface CheckboxProps {
    id: string;
    checked: boolean;
    onChange: (e: any) => void;
    indeterminate?: boolean;
    disabled?: boolean;
}

const Checkbox: React.FC<CheckboxProps> = ({ id, checked, onChange, indeterminate = false, disabled = false }) => {
    const ref = React.useRef<HTMLInputElement>(null)

    React.useEffect(() => {
        if (ref.current) {
            ref.current.indeterminate = indeterminate
        }
    }, [indeterminate])

    return (
        <div className="flex items-center">
            <input
                ref={ref}
                type="checkbox"
                id={id}
                checked={checked}
                onChange={onChange}
                disabled={disabled}
                className="h-5 w-5 appearance-none border-2 border-[#00B2A1] rounded-sm checked:border-[#00B2A1] 
                   checked:bg-white checked:after:content-[''] checked:after:border-b-2 checked:after:border-r-2 
                   checked:after:border-[#00B2A1] checked:after:w-1.5 checked:after:h-3.5 checked:after:block 
                   checked:after:rotate-45 checked:after:translate-x-[5px] checked:after:translate-y-[-1px] 
                   focus:outline-none focus:ring-0 relative disabled:opacity-50 disabled:cursor-not-allowed"
            />
        </div>
    );
};

export default Checkbox;
