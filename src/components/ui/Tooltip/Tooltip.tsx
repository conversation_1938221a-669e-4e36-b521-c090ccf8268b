import { Info } from "lucide-react";
import React, { useState } from "react";
interface tooltip {
  text: string;
}

const Tooltip: React.FC<tooltip> = ({ text }) => {
  const [isVisible, setIsVisible] = useState(false);
  return (
    <div
      className="relative flex items-center"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      <span className="text-gray-600 cursor-pointer flex items-center">
        <button>
          <Info className="w-4 h-4 text-gray-400" />
        </button>
      </span>
      {isVisible && (
        <div className="absolute left-1/2 -translate-x-1/2 bottom-full w-56 p-2 text-sm text-gray-700  bg-white rounded-lg shadow-xl">
          <span className="flex justify-center">{text}</span>
        </div>
      )}
    </div>
  );
};

export default Tooltip;
