'use client';

interface CardProps {
    children: React.ReactNode;
    className?: string;
    padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
    shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
    border?: boolean;
    rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
    hover?: boolean;
    onClick?: () => void;
}

interface CardHeaderProps {
    children: React.ReactNode;
    className?: string;
}

interface CardContentProps {
    children: React.ReactNode;
    className?: string;
}

interface CardFooterProps {
    children: React.ReactNode;
    className?: string;
}

export const Card = ({
    children,
    className = '',
    padding = 'md',
    shadow = 'sm',
    border = true,
    rounded = 'lg',
    hover = false,
    onClick
}: CardProps) => {
    const paddingClasses = {
        none: '',
        sm: 'p-3',
        md: 'p-4',
        lg: 'p-6',
        xl: 'p-8'
    };

    const shadowClasses = {
        none: '',
        sm: 'shadow-sm',
        md: 'shadow-md',
        lg: 'shadow-lg',
        xl: 'shadow-xl'
    };

    const roundedClasses = {
        none: '',
        sm: 'rounded-sm',
        md: 'rounded-md',
        lg: 'rounded-lg',
        xl: 'rounded-xl',
        full: 'rounded-full'
    };

    return (
        <div
            className={`
                bg-white
                ${paddingClasses[padding]}
                ${shadowClasses[shadow]}
                ${border ? 'border border-gray-200' : ''}
                ${roundedClasses[rounded]}
                ${hover ? 'hover:shadow-md transition-shadow duration-200' : ''}
                ${onClick ? 'cursor-pointer' : ''}
                ${className}
            `}
            onClick={onClick}
        >
            {children}
        </div>
    );
};

export const CardHeader = ({ children, className = '' }: CardHeaderProps) => {
    return (
        <div className={`pb-4 border-b border-gray-200 mb-4 ${className}`}>
            {children}
        </div>
    );
};

export const CardContent = ({ children, className = '' }: CardContentProps) => {
    return (
        <div className={className}>
            {children}
        </div>
    );
};

export const CardFooter = ({ children, className = '' }: CardFooterProps) => {
    return (
        <div className={`pt-4 border-t border-gray-200 mt-4 ${className}`}>
            {children}
        </div>
    );
};

export default Card;