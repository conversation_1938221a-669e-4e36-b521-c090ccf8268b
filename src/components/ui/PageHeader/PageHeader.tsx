'use client';

import { InformationCircleIcon } from '@heroicons/react/24/outline';

interface BreadcrumbItem {
    label: string;
    href?: string;
    onClick?: () => void;
}

interface PageHeaderAction {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'tertiary' | 'ghost';
}

interface PageHeaderProps {
    title: string;
    description?: string;
    breadcrumbs?: BreadcrumbItem[];
    actions?: PageHeaderAction[];
    showAbout?: boolean;
    onAboutClick?: () => void;
    className?: string;
}

const Breadcrumb = ({ items }: { items: BreadcrumbItem[] }) => {
    if (!items || items.length === 0) return null;

    return (
        <nav className="text-sm mb-2">
            <ul className="flex flex-wrap items-center">
                {items.map((item, index) => {
                    const isLast = index === items.length - 1;
                    
                    return (
                        <li key={`${item.label}-${index}`} className="flex items-center">
                            {index > 0 && (
                                <span className="mx-2 text-gray-400 text-sm">{'>'}</span>
                            )}
                            
                            {isLast ? (
                                <span className="text-gray-500">{item.label}</span>
                            ) : item.href ? (
                                <a
                                    href={item.href}
                                    className="text-teal-500 hover:underline transition-colors duration-200"
                                >
                                    {item.label}
                                </a>
                            ) : (
                                <button
                                    onClick={item.onClick}
                                    className="text-teal-500 hover:underline transition-colors duration-200"
                                >
                                    {item.label}
                                </button>
                            )}
                        </li>
                    );
                })}
            </ul>
        </nav>
    );
};

const ActionButton = ({ action }: { action: PageHeaderAction }) => {
    const baseClasses = 'inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2';
    
    const variantClasses = {
        primary: 'bg-[#00B2A1] text-white hover:bg-[#009688]',
        secondary: 'bg-white text-teal-500 border-2 border-teal-500 hover:bg-teal-500 hover:text-white',
        tertiary: 'bg-white text-gray-500 border-2 border-gray-500 hover:bg-gray-500 hover:text-white',
        ghost: 'bg-transparent text-teal-500 hover:bg-teal-50'
    };

    return (
        <button
            onClick={action.onClick}
            className={`${baseClasses} ${variantClasses[action.variant || 'ghost']}`}
        >
            {action.icon && (
                <span className="mr-2">
                    {action.icon}
                </span>
            )}
            {action.label}
        </button>
    );
};

export const PageHeader = ({
    title,
    description,
    breadcrumbs = [],
    actions = [],
    showAbout = true,
    onAboutClick,
    className = ''
}: PageHeaderProps) => {
    const allActions = [...actions];
    
    // Add About action if enabled
    if (showAbout) {
        allActions.push({
            label: 'About',
            icon: <InformationCircleIcon className="w-4 h-4" />,
            onClick: onAboutClick || (() => console.log('About clicked')),
            variant: 'ghost' as const
        });
    }

    return (
        <div className={`bg-white border-b border-gray-200 px-6 py-4 ${className}`}>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div className="flex-1 min-w-0">
                    <Breadcrumb items={breadcrumbs} />
                    <h1 className="text-2xl font-bold text-gray-900 truncate">
                        {title}
                    </h1>
                    {description && (
                        <p className="mt-1 text-gray-600">
                            {description}
                        </p>
                    )}
                </div>
                
                {allActions.length > 0 && (
                    <div className="mt-4 sm:mt-0 sm:ml-6 flex flex-wrap gap-2">
                        {allActions.map((action, index) => (
                            <ActionButton
                                key={`${action.label}-${index}`}
                                action={action}
                            />
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default PageHeader;