'use client';

interface RadioOption {
    value: string;
    label: string;
    disabled?: boolean;
}

interface RadioProps {
    name: string;
    options: RadioOption[];
    value?: string;
    onChange?: (value: string) => void;
    disabled?: boolean;
    className?: string;
    orientation?: 'horizontal' | 'vertical';
}

export const Radio = ({
    name,
    options,
    value,
    onChange,
    disabled = false,
    className = '',
    orientation = 'vertical'
}: RadioProps) => {
    const handleChange = (optionValue: string) => {
        if (disabled) return;
        onChange?.(optionValue);
    };

    const containerClasses = orientation === 'horizontal' 
        ? 'flex flex-row space-x-4' 
        : 'flex flex-col space-y-2';

    return (
        <div className={`${containerClasses} ${className}`}>
            {options.map((option) => {
                const isDisabled = disabled || option.disabled;
                const isChecked = value === option.value;
                
                return (
                    <div key={option.value} className="flex items-center">
                        <div className="relative">
                            <input
                                type="radio"
                                id={`${name}-${option.value}`}
                                name={name}
                                value={option.value}
                                checked={isChecked}
                                onChange={() => handleChange(option.value)}
                                disabled={isDisabled}
                                className="sr-only"
                            />
                            <button
                                type="button"
                                role="radio"
                                aria-checked={isChecked}
                                onClick={() => handleChange(option.value)}
                                disabled={isDisabled}
                                className={`
                                    w-4 h-4 rounded-full border-2 transition-all duration-200 ease-in-out relative flex items-center justify-center
                                    focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2
                                    ${isChecked
                                        ? 'border-teal-500 bg-teal-500'
                                        : 'border-gray-300 bg-white hover:border-gray-400'
                                    }
                                    ${isDisabled
                                        ? 'opacity-50 cursor-not-allowed'
                                        : 'cursor-pointer'
                                    }
                                `}
                            >
                                {isChecked && (
                                    <div className="w-2 h-2 bg-white rounded-full" />
                                )}
                            </button>
                        </div>
                        <label
                            htmlFor={`${name}-${option.value}`}
                            className={`ml-2 text-sm font-medium ${
                                isDisabled 
                                    ? 'text-gray-400 cursor-not-allowed' 
                                    : 'text-gray-700 cursor-pointer'
                            }`}
                            onClick={!isDisabled ? () => handleChange(option.value) : undefined}
                        >
                            {option.label}
                        </label>
                    </div>
                );
            })}
        </div>
    );
};

export default Radio;