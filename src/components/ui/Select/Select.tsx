'use client';

import { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { ChevronDownIcon, XMarkIcon } from '@heroicons/react/24/outline';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectProps {
  // Required props
  value: string;
  onChange: (value: string) => void;
  options: SelectOption[];
  
  // Optional props
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  errorMessage?: string;
  className?: string;
  width?: number | 'full';
  clearable?: boolean;
  searchable?: boolean;
  
  // Styling props
  height?: number;
  fontSize?: number;
  borderRadius?: number;
  borderColor?: string;
  focusBorderColor?: string;
  backgroundColor?: string;
}

export const Select = ({
  value,
  onChange,
  options,
  placeholder = 'Select an option...',
  disabled = false,
  error = false,
  errorMessage,
  className = '',
  width = 'full',
  clearable = false,
  searchable = false,
  height = 32,
  fontSize = 14,
  borderRadius = 6,
  borderColor = '#d1d5db',
  focusBorderColor = '#00B2A1',
  backgroundColor = '#ffffff',
}: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const selectedOption = options.find(option => option.value === value);
  
  const filteredOptions = searchable 
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (isOpen && searchable && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // Update dropdown position on scroll/resize when open
  useEffect(() => {
    if (!isOpen) return;

    const handlePositionUpdate = () => {
      updateDropdownPosition();
    };

    window.addEventListener('scroll', handlePositionUpdate, true);
    window.addEventListener('resize', handlePositionUpdate);

    return () => {
      window.removeEventListener('scroll', handlePositionUpdate, true);
      window.removeEventListener('resize', handlePositionUpdate);
    };
  }, [isOpen]);

  const updateDropdownPosition = () => {
    if (selectRef.current) {
      const rect = selectRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }
  };

  const handleToggleOpen = () => {
    if (!disabled) {
      if (!isOpen) {
        updateDropdownPosition();
      }
      setIsOpen(!isOpen);
    }
  };

  const handleOptionClick = (optionValue: string) => {
    if (!disabled) {
      onChange(optionValue);
      setIsOpen(false);
      setSearchTerm('');
    }
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!disabled && clearable) {
      onChange('');
      setIsOpen(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        if (!searchable) {
          e.preventDefault();
          handleToggleOpen();
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          // Handle arrow navigation through options
          const currentIndex = filteredOptions.findIndex(opt => opt.value === value);
          const nextIndex = Math.min(currentIndex + 1, filteredOptions.length - 1);
          if (filteredOptions[nextIndex] && !filteredOptions[nextIndex].disabled) {
            onChange(filteredOptions[nextIndex].value);
          }
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (isOpen) {
          const currentIndex = filteredOptions.findIndex(opt => opt.value === value);
          const prevIndex = Math.max(currentIndex - 1, 0);
          if (filteredOptions[prevIndex] && !filteredOptions[prevIndex].disabled) {
            onChange(filteredOptions[prevIndex].value);
          }
        }
        break;
    }
  };

  const selectStyles = {
    width: width === 'full' ? '100%' : `${width}px`,
    height: `${height}px`,
    fontSize: `${fontSize}px`,
    borderRadius: `${borderRadius}px`,
    borderColor: error ? '#ef4444' : (isOpen ? focusBorderColor : borderColor),
    backgroundColor,
    boxShadow: isOpen ? `0 0 0 3px ${focusBorderColor}20` : 'none',
  };

  return (
    <div className={`relative ${className}`}>
      <div
        ref={selectRef}
        className={`
          relative cursor-pointer border transition-all duration-200
          ${disabled ? 'cursor-not-allowed opacity-50 bg-gray-50' : 'hover:border-gray-400'}
          ${error ? 'border-red-500' : ''}
        `}
        style={selectStyles}
        onClick={handleToggleOpen}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <div className="flex items-center justify-between h-full px-3">
          <div className="flex-1 truncate">
            {searchable && isOpen ? (
              <input
                ref={inputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onClick={(e) => e.stopPropagation()}
                placeholder={placeholder}
                className="w-full bg-transparent outline-none text-sm"
                style={{ fontSize: `${fontSize}px` }}
              />
            ) : (
              <span className={`text-sm ${selectedOption ? 'text-gray-900' : 'text-gray-500'}`}>
                {selectedOption ? selectedOption.label : placeholder}
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-1">
            {clearable && value && !disabled && (
              <button
                onClick={handleClear}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                tabIndex={-1}
              >
                <XMarkIcon className="w-4 h-4 text-gray-400" />
              </button>
            )}
            <ChevronDownIcon 
              className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                isOpen ? 'transform rotate-180' : ''
              }`} 
            />
          </div>
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && !disabled && typeof document !== 'undefined' && createPortal(
        <div 
          className="fixed z-[9999] bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`,
          }}
        >
          {filteredOptions.length === 0 ? (
            <div className="px-3 py-2 text-sm text-gray-500">
              {searchable ? 'No options found' : 'No options available'}
            </div>
          ) : (
            <ul role="listbox" className="py-1">
              {filteredOptions.map((option) => (
                <li
                  key={option.value}
                  role="option"
                  aria-selected={option.value === value}
                  className={`
                    px-3 py-2 text-sm cursor-pointer transition-colors
                    ${option.disabled 
                      ? 'text-gray-400 cursor-not-allowed' 
                      : 'text-gray-900 hover:bg-gray-50'
                    }
                    ${option.value === value ? 'bg-teal-50 text-teal-900' : ''}
                  `}
                  onClick={() => !option.disabled && handleOptionClick(option.value)}
                >
                  {option.label}
                </li>
              ))}
            </ul>
          )}
        </div>,
        document.body
      )}

      {/* Error message */}
      {error && errorMessage && (
        <p className="mt-1 text-sm text-red-600">{errorMessage}</p>
      )}
    </div>
  );
};