'use client';

import { useState, useRef, type DragEvent } from 'react';

interface UploadProps {
    // File handling props
    uploadedFile: File | null;
    onFileUpload: (file: File) => void;
    onClearFile?: () => void;
    
    // Upload state props
    isUploading?: boolean;
    uploadError?: string | null;
    
    // Configuration props
    acceptedFileTypes?: string;
    maxFileSize?: number; // in bytes
    
    // Content customization props
    title?: string;
    description?: string;
    browseButtonText?: string;
    sampleButtonText?: string;
    sampleFileData?: string;
    sampleFileName?: string;
    
    // Styling props
    className?: string;
    containerClassName?: string;
    height?: number | 'auto';
    borderStyle?: 'dashed' | 'solid' | 'none';
    borderColor?: string;
    dragBorderColor?: string;
    backgroundColor?: string;
    dragBackgroundColor?: string;
    
    // Callback props
    onSampleFile?: () => void;
    
    // Loading component
    LoadingSpinner?: React.ComponentType<{ size?: 'small' | 'medium' | 'large' }>;
}

export const Upload = ({
    // File handling props
    uploadedFile,
    onFileUpload,
    onClearFile,
    
    // Upload state props
    isUploading = false,
    uploadError = null,
    
    // Configuration props
    acceptedFileTypes = '.csv,.json,.xls,.xlsx',
    maxFileSize,
    
    // Content customization props
    title = 'Add a file to create a new Narrative',
    description = 'Supported formats: CSV, JSON, XLS, XLSX',
    browseButtonText = 'Browse Files',
    sampleButtonText = 'Try Sample File',
    sampleFileData,
    sampleFileName = 'sample-data.csv',
    
    // Styling props
    className = '',
    containerClassName = '',
    height = 'auto',
    borderStyle = 'dashed',
    borderColor = '#d1d5db',
    dragBorderColor = '#14b8a6',
    backgroundColor = 'transparent',
    dragBackgroundColor = '#f0fdfa',
    
    // Callback props
    onSampleFile,
    
    // Loading component
    LoadingSpinner,
}: UploadProps) => {
    const [isDragging, setIsDragging] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            const file = event.target.files[0];
            
            // Check file size if maxFileSize is specified
            if (maxFileSize && file.size > maxFileSize) {
                return;
            }
            
            onFileUpload(file);
        }
    };

    const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(false);

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            const file = e.dataTransfer.files[0];
            
            // Check file size if maxFileSize is specified
            if (maxFileSize && file.size > maxFileSize) {
                return;
            }
            
            onFileUpload(file);
        }
    };

    const handleBrowseClick = () => {
        fileInputRef.current?.click();
    };

    const handleSampleFile = () => {
        if (onSampleFile) {
            onSampleFile();
        } else if (sampleFileData) {
            // Create a sample file in memory
            const sampleFile = new File([sampleFileData], sampleFileName, {
                type: 'text/csv',
            });
            onFileUpload(sampleFile);
        }
    };

    const handleClearFile = () => {
        if (onClearFile) {
            onClearFile();
        }
        // Clear the file input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const getBorderClass = () => {
        if (borderStyle === 'none') return '';
        const style = borderStyle === 'dashed' ? 'border-dashed' : 'border-solid';
        return `border-4 ${style}`;
    };

    const getHeightClass = () => {
        return height === 'auto' ? 'h-fit' : `h-${height}`;
    };

    const [isHovering, setIsHovering] = useState(false);
    
    const containerStyles = {
        borderColor: isDragging ? dragBorderColor : isHovering && !uploadedFile && !isUploading ? '#14b8a6' : borderColor,
    };

    const handleContainerClick = () => {
        if (!uploadedFile && !isUploading) {
            handleBrowseClick();
        }
    };

    return (
        <div className={`w-full ${containerClassName}`}>
            <div
                className={`flex flex-col items-start justify-center px-8 py-2 ${getBorderClass()} rounded-xl ${getHeightClass()} transition-all duration-200 ${!uploadedFile && !isUploading ? 'cursor-pointer' : ''} ${className}`}
                style={containerStyles}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={handleContainerClick}
                onMouseEnter={() => setIsHovering(true)}
                onMouseLeave={() => setIsHovering(false)}
            >
                <div className="flex flex-row items-center justify-between w-full h-[72px]">
                    <div className="flex flex-row items-center">
                        <input
                            type="file"
                            onChange={handleFileUpload}
                            className="hidden"
                            id="fileUpload"
                            ref={fileInputRef}
                            accept={acceptedFileTypes}
                        />

                        {/* Upload Icon */}
                        {isUploading && LoadingSpinner ? (
                            <LoadingSpinner size="small" />
                        ) : (
                            <svg
                                className="w-12 h-12 text-slate-400 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                />
                            </svg>
                        )}

                        <div className="flex flex-col items-start">
                            <div className="flex items-center">
                                {uploadedFile ? (
                                    <p className="text-xl text-left font-bold text-slate-700">
                                        {uploadedFile.name}
                                    </p>
                                ) : (
                                    <p className="text-xl text-left font-medium text-slate-400">
                                        Drag & drop your file or click to browse
                                    </p>
                                )}
                                {uploadedFile && onClearFile && (
                                    <button
                                        onClick={handleClearFile}
                                        className="flex flex-row items-center text-sm ml-2 border-2 border-red-500 rounded-full text-red-500 hover:text-red-700 transition p-1"
                                        disabled={isUploading}
                                    >
                                        <svg
                                            className="w-4 h-4"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth="2"
                                                d="M6 18L18 6M6 6l12 12"
                                            />
                                        </svg>
                                    </button>
                                )}
                            </div>
                            {uploadError && (
                                <p className="text-sm text-red-500 mt-1">
                                    {uploadError}
                                </p>
                            )}
                        </div>

                    </div>
                </div>
            </div>
        </div>
    );
};