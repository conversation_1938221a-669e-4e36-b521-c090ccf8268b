'use client'

import { useState } from 'react'
import { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { TableFiltersProps } from './types/table.types'
import { cn } from './utils/tableHelpers'

export const TableFilters = ({ table, className }: TableFiltersProps) => {
  const [showColumnFilters, setShowColumnFilters] = useState(false)
  const globalFilter = table.getState().globalFilter
  const columnFilters = table.getState().columnFilters

  const hasActiveFilters = globalFilter || columnFilters.length > 0

  return (
    <div className={cn('p-4 border-b border-slate-200 bg-gray-50', className)}>
      <div className="flex items-center justify-between gap-4">
        {/* Global search */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              value={globalFilter ?? ''}
              onChange={(e) => table.setGlobalFilter(e.target.value)}
              placeholder="Search all columns..."
              className={cn(
                'w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md',
                'focus:border-teal-500 focus:ring-2 focus:ring-teal-500',
                'placeholder:text-gray-400'
              )}
            />
            {globalFilter && (
              <button
                onClick={() => table.setGlobalFilter('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Filter controls */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowColumnFilters(!showColumnFilters)}
            className={cn(
              'flex items-center gap-2 px-3 py-2 text-sm border rounded-md transition-colors',
              showColumnFilters || hasActiveFilters
                ? 'bg-[#00B2A1] text-white border-[#00B2A1]'
                : 'border-gray-300 hover:bg-gray-50 focus:border-teal-500 focus:ring-2 focus:ring-teal-500'
            )}
          >
            <FunnelIcon className="w-4 h-4" />
            Column Filters
            {columnFilters.length > 0 && (
              <span className="bg-white text-[#00B2A1] px-1.5 py-0.5 rounded-full text-xs font-medium">
                {columnFilters.length}
              </span>
            )}
          </button>

          {hasActiveFilters && (
            <button
              onClick={() => {
                table.resetGlobalFilter()
                table.resetColumnFilters()
              }}
              className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Clear All
            </button>
          )}
        </div>
      </div>

      {/* Column-specific filters */}
      {showColumnFilters && (
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {table.getAllColumns()
            .filter((column) => column.getCanFilter())
            .map((column) => {
              const filterValue = column.getFilterValue()
              const columnDef = column.columnDef
              
              return (
                <div key={column.id} className="space-y-1">
                  <label className="text-xs font-medium text-gray-700">
                    {typeof columnDef.header === 'string' ? columnDef.header : column.id}
                  </label>
                  <div className="relative">
                    <input
                      value={(filterValue ?? '') as string}
                      onChange={(e) => column.setFilterValue(e.target.value)}
                      placeholder={`Filter ${column.id}...`}
                      className={cn(
                        'w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md',
                        'focus:border-teal-500 focus:ring-1 focus:ring-teal-500',
                        'placeholder:text-gray-400'
                      )}
                    />
                    {filterValue ? (
                      <button
                        onClick={() => column.setFilterValue('')}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        <XMarkIcon className="w-3 h-3" />
                      </button>
                    ) : null}
                  </div>
                </div>
              )
            })}
        </div>
      )}
    </div>
  )
}