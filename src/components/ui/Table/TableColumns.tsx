"use client";

import { useState, useEffect } from "react";
import {
  EyeIcon,
  EyeSlashIcon,
  Bars3Icon,
  XMarkIcon,
  LockClosedIcon,
  CogIcon,
} from "@heroicons/react/24/outline";
import { ColumnManagementProps } from "./types/table.types";
import { cn } from "./utils/tableHelpers";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface SortableColumnItemProps {
  column: {
    id: string;
    columnDef: unknown;
    getIsVisible: () => boolean;
  };
  isVisible: boolean;
  pinned: "left" | "right" | null;
  displayName: string;
  isLocked: boolean;
  onToggleVisibility: (columnId: string) => void;
  onPinColumn: (columnId: string, position: "left" | "right" | null) => void;
}

const SortableColumnItem = ({
  column,
  isVisible,
  displayName,
  isLocked,
  onToggleVisibility,
  onPinColumn,
}: SortableColumnItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: column.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "py-1",
        isDragging && "opacity-50"
      )}
    >
      <div className="flex items-center justify-between border border-gray-300 rounded-lg px-4 py-3">
        <div className="flex items-center gap-3">
          {/* Visibility toggle */}
          <button
            onClick={() => onToggleVisibility(column.id)}
            className={cn(
              "text-gray-400 hover:text-gray-600",
              isVisible && "text-gray-600"
            )}
          >
            {isVisible ? (
              <EyeIcon className="w-5 h-5" />
            ) : (
              <EyeSlashIcon className="w-5 h-5" />
            )}
          </button>

          {/* Column name */}
          <span className="text-sm font-medium text-gray-900">
            {displayName}
          </span>
        </div>

        {/* Controls */}
        <div className="flex items-center gap-2">
          {/* Lock indicator for pinned columns */}
          {isLocked ? (
            <button
              onClick={() => onPinColumn(column.id, null)}
              className="text-gray-400 hover:text-gray-600"
              title="Column is locked (click to unlock)"
            >
              <LockClosedIcon className="w-4 h-4" />
            </button>
          ) : (
            /* Drag handle for non-locked columns */
            <div 
              {...attributes}
              {...listeners}
              className="text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing"
            >
              <Bars3Icon className="w-4 h-4" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface TableColumnsExtendedProps extends ColumnManagementProps {
  trigger?: React.ReactNode;
}

export const TableColumns = ({ table, className, trigger }: TableColumnsExtendedProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [, forceUpdate] = useState({});

  // Force component to re-render when table state changes
  useEffect(() => {
    const rerender = () => forceUpdate({});
    
    // Subscribe to table state changes by adding event listeners
    const originalOnStateChange = table.options.onStateChange;
    table.options.onStateChange = (updater) => {
      if (originalOnStateChange) {
        originalOnStateChange(updater);
      }
      rerender();
    };

    return () => {
      table.options.onStateChange = originalOnStateChange;
    };
  }, [table]);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Get columns in the current display order
  const allColumns = table
    .getAllColumns()
    .filter(
      (column) =>
        typeof column.accessorFn !== "undefined" && column.getCanHide()
    );

  // Sort columns by current table order
  const currentOrder = table.getState().columnOrder;
  const columns = currentOrder.length > 0 
    ? currentOrder
        .map(id => allColumns.find(col => col.id === id))
        .filter((col): col is NonNullable<typeof col> => col !== undefined)
        .concat(allColumns.filter(col => !currentOrder.includes(col.id)))
    : allColumns;

  const handleToggleVisibility = (columnId: string) => {
    const column = table.getColumn(columnId);
    if (column) {
      column.toggleVisibility();
    }
  };

  const handlePinColumn = (
    columnId: string,
    position: "left" | "right" | null
  ) => {
    const column = table.getColumn(columnId);
    if (column) {
      if (position === "left") {
        column.pin("left");
      } else if (position === "right") {
        column.pin("right");
      } else {
        column.pin(false);
      }
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = columns.findIndex((column) => column.id === active.id);
      const newIndex = columns.findIndex((column) => column.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newColumnOrder = arrayMove(
          columns.map((col) => col.id),
          oldIndex,
          newIndex
        );
        table.setColumnOrder(newColumnOrder);
      }
    }
  };


  const isPinned = (columnId: string): "left" | "right" | null => {
    const column = table.getColumn(columnId);
    if (!column) return null;
    
    const pinned = column.getIsPinned();
    return pinned === "left" ? "left" : pinned === "right" ? "right" : null;
  };

  if (!isOpen) {
    return trigger ? (
      <div onClick={() => setIsOpen(true)} className={className}>
        {trigger}
      </div>
    ) : (
      <button
        onClick={() => setIsOpen(true)}
        className={cn(
          "p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-md transition-colors",
          className
        )}
      >
        <CogIcon className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className={cn("relative", className)}>
      <div className="absolute right-0 top-0 z-50 w-80 bg-white border border-gray-200 rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex flex-col items-start justify-between p-4 pb-2 gap-1">
          <div className="flex flex-row items-center justify-between w-full">
            <h3 className="text-lg font-semibold text-gray-900">
              Show/Hide Columns
            </h3>
            <button
              onClick={() => setIsOpen(false)}
              className="p-1 hover:bg-gray-100 rounded text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
          {columns.some(column => !column.getIsVisible()) && (
            <button
              onClick={() => table.toggleAllColumnsVisible(true)}
              className="text-teal-500 hover:text-teal-600 text-sm font-medium hover:underline"
            >
              View All Columns
            </button>
          )}
        </div>

        {/* Column list */}
        <div className="max-h-80 overflow-y-auto px-4 py-2">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={columns.map((col) => col.id)}
              strategy={verticalListSortingStrategy}
            >
              {columns.map((column) => {
                // Use TanStack Table's built-in method for visibility
                const isVisible = column.getIsVisible();
                const pinned = isPinned(column.id);
                const columnDef = column.columnDef;
                const displayName =
                  typeof columnDef.header === "string"
                    ? columnDef.header
                    : column.id;
                const isLocked = pinned !== null;

                return (
                  <SortableColumnItem
                    key={column.id}
                    column={column}
                    isVisible={isVisible}
                    pinned={pinned}
                    displayName={displayName}
                    isLocked={isLocked}
                    onToggleVisibility={handleToggleVisibility}
                    onPinColumn={handlePinColumn}
                  />
                );
              })}
            </SortableContext>
          </DndContext>
        </div>
      </div>
    </div>
  );
};
