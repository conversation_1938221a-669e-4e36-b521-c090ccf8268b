/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect, useRef, ReactNode } from 'react';
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface EditableCellProps<T = any> {
  // Required props
  value: T;
  onSave: (newValue: T) => void | Promise<void>;
  
  // Render functions
  renderDisplay: (value: T) => ReactNode;
  renderEdit: (value: T, onChange: (newValue: T) => void, onCommit: () => void, onCancel: () => void) => ReactNode;
  
  // Optional props
  onCancel?: () => void;
  validate?: (value: T) => string | null;
  disabled?: boolean;
  autoSave?: boolean; // Auto-save on blur/enter, or require explicit save/cancel
  className?: string;
  editingClassName?: string;
  
  // Loading states
  isSaving?: boolean;
  
  // Edit mode control (for external control)
  isEditing?: boolean;
  onEditingChange?: (editing: boolean) => void;
}

export const EditableCell = <T,>({
  value,
  onSave,
  renderDisplay,
  renderEdit,
  onCancel,
  validate,
  disabled = false,
  autoSave = true,
  className = '',
  editingClassName = '',
  isSaving = false,
  isEditing: externalIsEditing,
  onEditingChange,
}: EditableCellProps<T>) => {
  const [internalIsEditing, setInternalIsEditing] = useState(false);
  const [editValue, setEditValue] = useState<T>(value);
  const [error, setError] = useState<string | null>(null);
  const [isLocalSaving, setIsLocalSaving] = useState(false);
  
  const cellRef = useRef<HTMLDivElement>(null);
  const isControlled = externalIsEditing !== undefined;
  const isEditing = isControlled ? externalIsEditing : internalIsEditing;
  const setIsEditing = isControlled ? onEditingChange! : setInternalIsEditing;

  // Update edit value when prop value changes
  useEffect(() => {
    if (!isEditing) {
      setEditValue(value);
      setError(null);
    }
  }, [value, isEditing]);

  // Handle clicks outside to save (if autoSave is enabled)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (cellRef.current && !cellRef.current.contains(event.target as Node) && isEditing) {
        if (autoSave) {
          handleSave();
        } else {
          handleCancel();
        }
      }
    };

    if (isEditing) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isEditing, editValue, autoSave]);

  const handleEdit = () => {
    if (!disabled && !isSaving && !isLocalSaving) {
      setEditValue(value);
      setError(null);
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (disabled || isSaving || isLocalSaving) return;

    // Validate if validator is provided
    if (validate) {
      const validationError = validate(editValue);
      if (validationError) {
        setError(validationError);
        return;
      }
    }

    // Don't save if value hasn't changed
    if (editValue === value) {
      setIsEditing(false);
      return;
    }

    try {
      setIsLocalSaving(true);
      setError(null);
      
      await onSave(editValue);
      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Save failed');
    } finally {
      setIsLocalSaving(false);
    }
  };

  const handleCancel = () => {
    if (disabled || isSaving || isLocalSaving) return;
    
    setEditValue(value);
    setError(null);
    setIsEditing(false);
    onCancel?.();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;

    switch (e.key) {
      case 'Escape':
        e.preventDefault();
        handleCancel();
        break;
      case 'Enter':
        if (!e.shiftKey) { // Allow Shift+Enter for multi-line inputs
          e.preventDefault();
          handleSave();
        }
        break;
    }
  };

  const isCurrentlySaving = isSaving || isLocalSaving;

  if (isEditing) {
    return (
      <div 
        ref={cellRef}
        className={`relative ${editingClassName}`}
        onKeyDown={handleKeyDown}
      >
        <div className="flex items-center gap-2">
          <div className="flex-1">
            {renderEdit(editValue, setEditValue, handleSave, handleCancel)}
          </div>
          
          {!autoSave && (
            <div className="flex items-center gap-1">
              <button
                onClick={handleSave}
                disabled={isCurrentlySaving}
                className="p-1 text-green-600 hover:bg-green-50 rounded transition-colors disabled:opacity-50"
                title="Save"
              >
                <CheckIcon className={`w-4 h-4 ${isCurrentlySaving ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={handleCancel}
                disabled={isCurrentlySaving}
                className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors disabled:opacity-50"
                title="Cancel"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
        
        {error && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded border border-red-200 whitespace-nowrap z-10">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      ref={cellRef}
      className={`
        cursor-pointer hover:bg-gray-50 transition-colors p-1 -m-1 rounded
        ${disabled ? 'cursor-not-allowed opacity-50' : ''}
        ${className}
      `}
      onClick={handleEdit}
      onDoubleClick={handleEdit}
      title={disabled ? undefined : 'Click to edit'}
    >
      {renderDisplay(value)}
      {isCurrentlySaving && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
          <div className="w-4 h-4 border-2 border-teal-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
};