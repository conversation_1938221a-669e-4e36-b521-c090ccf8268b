'use client';

import { ReactNode } from 'react';
import { EditableCell } from './EditableCell';
import { Select, SelectOption } from '@/components/ui/Select/Select';

interface EditableSelectCellProps {
  // Required props
  value: string;
  options: SelectOption[];
  onSave: (newValue: string) => void | Promise<void>;
  
  // Display customization
  renderDisplay?: (value: string, option?: SelectOption) => ReactNode;
  
  // Optional props
  placeholder?: string;
  disabled?: boolean;
  onCancel?: () => void;
  validate?: (value: string) => string | null;
  autoSave?: boolean;
  clearable?: boolean;
  searchable?: boolean;
  
  // Styling
  className?: string;
  editingClassName?: string;
  
  // Loading states
  isSaving?: boolean;
  
  // Edit mode control
  isEditing?: boolean;
  onEditingChange?: (editing: boolean) => void;
}

export const EditableSelectCell = ({
  value,
  options,
  onSave,
  renderDisplay,
  placeholder = 'Select...',
  disabled = false,
  onCancel,
  validate,
  autoSave = true,
  clearable = false,
  searchable = false,
  className = '',
  editingClassName = '',
  isSaving = false,
  isEditing,
  onEditingChange,
}: EditableSelectCellProps) => {
  const selectedOption = options.find(option => option.value === value);

  const defaultRenderDisplay = (displayValue: string, option?: SelectOption) => {
    if (!displayValue || !option) {
      return <span className="text-gray-500 italic">No selection</span>;
    }
    
    return (
      <span className="text-gray-900">
        {option.label}
      </span>
    );
  };

  const handleRenderDisplay = (displayValue: string) => {
    const option = options.find(opt => opt.value === displayValue);
    return renderDisplay ? renderDisplay(displayValue, option) : defaultRenderDisplay(displayValue, option);
  };

  const handleRenderEdit = (
    editValue: string,
    onChange: (newValue: string) => void,
    onCommit: () => void,
    onCancelEdit: () => void
  ) => {
    return (
      <Select
        value={editValue}
        onChange={(newValue) => {
          onChange(newValue);
          if (autoSave) {
            // Small delay to allow the onChange to update the state
            setTimeout(onCommit, 50);
          }
        }}
        options={options}
        placeholder={placeholder}
        disabled={disabled || isSaving}
        clearable={clearable}
        searchable={searchable}
        width="full"
        height={28} // Smaller height for table cells
        fontSize={14}
      />
    );
  };

  // Custom validation that checks if value exists in options
  const handleValidate = (validateValue: string) => {
    // Allow empty values if clearable
    if (!validateValue && clearable) {
      return null;
    }
    
    // Check if value exists in options
    if (validateValue && !options.find(option => option.value === validateValue)) {
      return 'Invalid selection';
    }
    
    // Run custom validation if provided
    if (validate) {
      return validate(validateValue);
    }
    
    return null;
  };

  return (
    <EditableCell
      value={value}
      onSave={onSave}
      onCancel={onCancel}
      renderDisplay={handleRenderDisplay}
      renderEdit={handleRenderEdit}
      validate={handleValidate}
      disabled={disabled}
      autoSave={autoSave}
      className={className}
      editingClassName={editingClassName}
      isSaving={isSaving}
      isEditing={isEditing}
      onEditingChange={onEditingChange}
    />
  );
};

// Utility function to create status badge display for common use case
export const createStatusBadgeDisplay = (
  getStatusClasses: (status: string) => string
) => {
  const StatusBadge = (value: string, option?: SelectOption) => {
    if (!value || !option) {
      return <span className="text-gray-500 italic">No status</span>;
    }
    
    return (
      <span className={getStatusClasses(value)}>
        {option.label}
      </span>
    );
  };
  StatusBadge.displayName = 'StatusBadge';
  return StatusBadge;
};

// Common status options for typical use cases
export const COMMON_STATUS_OPTIONS: SelectOption[] = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'pending', label: 'Pending' },
  { value: 'archived', label: 'Archived' },
];

export const COMMON_ROLE_OPTIONS: SelectOption[] = [
  { value: 'admin', label: 'Administrator' },
  { value: 'manager', label: 'Manager' },
  { value: 'user', label: 'User' },
  { value: 'viewer', label: 'Viewer' },
];

export const COMMON_PRIORITY_OPTIONS: SelectOption[] = [
  { value: 'high', label: 'High Priority' },
  { value: 'medium', label: 'Medium Priority' },
  { value: 'low', label: 'Low Priority' },
];