import { useState, useMemo } from 'react'
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getGroupedRowModel,
  getExpandedRowModel,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  ColumnOrderState,
  ColumnPinningState,
  RowPinningState,
  PaginationState,
} from '@tanstack/react-table'
import { TableProps } from '../types/table.types'
import { mergeFeatures } from '../utils/tableHelpers'

export const useTableState = <TData>({
  data,
  columns,
  features: userFeatures = {},
  initialState,
  onStateChange,
  enableMultiSort = true,
  enableSortingRemoval = true,
  maxMultiSortColCount = 3,
}: TableProps<TData>) => {
  const features = mergeFeatures(userFeatures)

  const [sorting, setSorting] = useState<SortingState>(initialState?.sorting || [])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(initialState?.columnFilters || [])
  const [globalFilter, setGlobalFilter] = useState<string>(initialState?.globalFilter || '')
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(initialState?.columnVisibility || {})
  const [rowSelection, setRowSelection] = useState({})
  const [columnOrder, setColumnOrder] = useState<ColumnOrderState>(initialState?.columnOrder || [])
  const [columnPinning, setColumnPinning] = useState<ColumnPinningState>(initialState?.columnPinning || {})
  const [rowPinning, setRowPinning] = useState<RowPinningState>(initialState?.rowPinning || {})
  const [grouping, setGrouping] = useState<string[]>([])
  const [expanded, setExpanded] = useState({})
  const [pagination, setPagination] = useState<PaginationState>(
    initialState?.pagination || { pageIndex: 0, pageSize: 10 }
  )

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      columnVisibility,
      rowSelection,
      columnOrder,
      columnPinning,
      rowPinning,
      grouping,
      expanded,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onColumnOrderChange: setColumnOrder,
    onColumnPinningChange: setColumnPinning,
    onRowPinningChange: setRowPinning,
    onGroupingChange: setGrouping,
    onExpandedChange: setExpanded,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: features.sorting ? getSortedRowModel() : undefined,
    getFilteredRowModel: features.filtering ? getFilteredRowModel() : undefined,
    getPaginationRowModel: features.pagination ? getPaginationRowModel() : undefined,
    getGroupedRowModel: features.grouping ? getGroupedRowModel() : undefined,
    getExpandedRowModel: features.expanding ? getExpandedRowModel() : undefined,
    enableSorting: features.sorting,
    enableFilters: features.filtering,
    enableGlobalFilter: features.globalFilter,
    enableGrouping: features.grouping,
    enableExpanding: features.expanding,
    enableColumnResizing: features.columnResizing,
    enableColumnPinning: features.columnPinning,
    enableRowPinning: features.rowPinning,
    enableRowSelection: features.rowSelection,
    enableMultiSort,
    enableSortingRemoval,
    maxMultiSortColCount,
    debugTable: process.env.NODE_ENV === 'development',
  })

  const tableState = useMemo(() => ({
    sorting,
    columnFilters,
    globalFilter,
    columnVisibility,
    rowSelection,
    columnOrder,
    columnPinning,
    rowPinning,
    grouping,
    expanded,
    pagination,
  }), [
    sorting,
    columnFilters,
    globalFilter,
    columnVisibility,
    rowSelection,
    columnOrder,
    columnPinning,
    rowPinning,
    grouping,
    expanded,
    pagination,
  ])

  // Notify parent of state changes
  useState(() => {
    onStateChange?.(tableState)
  })

  return {
    table,
    features,
    state: tableState,
  }
}