import React from 'react'
import { ColumnDef } from '@tanstack/react-table'
import Checkbox from '@/components/ui/Checkbox/Checkbox'

interface TableCheckboxProps {
  checked: boolean
  indeterminate?: boolean
  onChange: (checked: boolean) => void
  disabled?: boolean
  density?: 'normal' | 'condensed'
}

const TableCheckbox: React.FC<TableCheckboxProps> = ({
  checked,
  indeterminate = false,
  onChange,
  disabled = false,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.checked)
  }

  // Create a unique ID for each checkbox instance
  const id = React.useMemo(() => `table-checkbox-${Math.random().toString(36).substring(2, 11)}`, [])

  return (
    <Checkbox
      id={id}
      checked={checked}
      onChange={handleChange}
      indeterminate={indeterminate}
      disabled={disabled}
    />
  )
}

export function createSelectionColumn<TData>(density?: 'normal' | 'condensed'): ColumnDef<TData> {
  return {
    id: 'select',
    header: ({ table }) => (
      <TableCheckbox
        checked={table.getIsAllRowsSelected()}
        indeterminate={table.getIsSomeRowsSelected()}
        onChange={table.getToggleAllRowsSelectedHandler()}
        density={density}
      />
    ),
    cell: ({ row }) => (
      <TableCheckbox
        checked={row.getIsSelected()}
        onChange={row.getToggleSelectedHandler()}
        disabled={!row.getCanSelect()}
        density={density}
      />
    ),
    enableSorting: false,
    enableColumnFilter: false,
    enableGlobalFilter: false,
    enableResizing: false,
    size: density === 'condensed' ? 30 : 40,
  }
}

export { TableCheckbox }