'use client'

import { TableCellProps } from './types/table.types'
import { cn } from './utils/tableHelpers'

export const TableCell = ({ 
  children, 
  className,
  onClick,
  density = 'normal'
}: TableCellProps) => {
  const getPaddingClasses = () => {
    return density === 'condensed' ? 'p-1.5' : 'p-3'
  }

  const getTextClasses = () => {
    return density === 'condensed' ? 'text-xs' : 'text-sm'
  }

  return (
    <td 
      className={cn(
        getPaddingClasses(),
        getTextClasses(),
        'text-center whitespace-nowrap text-ellipsis relative',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {children}
    </td>
  )
}