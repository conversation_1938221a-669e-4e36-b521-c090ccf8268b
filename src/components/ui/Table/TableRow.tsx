'use client'

import { TableRowProps } from './types/table.types'
import { cn } from './utils/tableHelpers'

export const TableRow = ({ 
  children, 
  className,
  isSelected = false,
  isPinned = false,
  level = 0,
  index = 0
}: TableRowProps) => {
  const getRowClasses = () => {
    // For regular data rows, use alternating colors
    const isEven = index % 2 === 0;
    if (isEven) {
      return 'bg-white border-b border-slate-100 hover:bg-gray-50'
    } else {
      return 'bg-[#f6f6f6] border-b border-slate-100 hover:bg-gray-100'
    }
  }

  return (
    <tr 
      className={cn(
        getRowClasses(),
        isSelected && 'bg-[#00B2A1]/10',
        isPinned && 'sticky z-20',
        className
      )}
    >
      {children}
    </tr>
  )
}