/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'

import { useState, useCallback } from 'react'
import { Table as TanStackTable } from '@tanstack/react-table'
import { CogIcon } from '@heroicons/react/24/outline'
import { TbBaselineDensitySmall, TbBaselineDensityMedium } from 'react-icons/tb'
import { Table } from './Table'
import { TableColumns } from './TableColumns'
import { TableProps, TableDensity, BulkAction } from './types/table.types'
import { PrimaryTabs } from '@/components/ui/Tabs/Tabs'

interface TableWithColumnManagementProps<TData> extends Omit<TableProps<TData>, 'onTableReady'> {
  showColumnManagementInHeader?: boolean
  showDensityToggle?: boolean
  onDensityChange?: (density: TableDensity) => void
  bulkActions?: BulkAction<TData>[]
  showBulkActions?: boolean
}

export const TableWithColumnManagement = <TData,>({
  showColumnManagementInHeader = true,
  showDensityToggle = false,
  density = 'normal',
  onDensityChange,
  bulkActions = [],
  showBulkActions = true,
  ...tableProps
}: TableWithColumnManagementProps<TData>) => {
  const [tableInstance, setTableInstance] = useState<TanStackTable<TData> | null>(null)
  const [currentDensity, setCurrentDensity] = useState<TableDensity>(density)

  const handleTableReady = useCallback((table: TanStackTable<TData>) => {
    setTableInstance(table)
  }, [])

  const handleDensityChange = useCallback((value: string) => {
    const newDensity = value as TableDensity
    setCurrentDensity(newDensity)
    onDensityChange?.(newDensity)
  }, [onDensityChange])

  // Get selected rows data
  const getSelectedRowsData = useCallback(() => {
    if (!tableInstance) return []
    return tableInstance.getSelectedRowModel().rows.map((row : any) => row.original)
  }, [tableInstance])

  // Get selected rows count
  const selectedRowsCount = tableInstance?.getSelectedRowModel().rows.length || 0
  const hasSelectedRows = selectedRowsCount > 0

  // Handle bulk action execution
  const handleBulkAction = useCallback((action: BulkAction<TData>) => {
    const selectedData = getSelectedRowsData()
    action.onClick(selectedData)
  }, [getSelectedRowsData])

  return (
    <div className="space-y-4">
      {/* Header with Column Management */}
      {showColumnManagementInHeader && tableInstance && (
        <div className="space-y-4">
          <div className="flex justify-end items-center">
            <div className="flex items-center">
              {showDensityToggle && (
                <PrimaryTabs
                  tabs={[
                    { 
                      value: 'normal', 
                      label: (
                        <div className="flex items-center gap-1.5">
                          <TbBaselineDensityMedium className="w-4 h-4" />
                          <span>Normal</span>
                        </div>
                      ), 
                      content: null 
                    },
                    { 
                      value: 'condensed', 
                      label: (
                        <div className="flex items-center gap-1.5">
                          <TbBaselineDensitySmall className="w-4 h-4" />
                          <span>Condensed</span>
                        </div>
                      ), 
                      content: null 
                    }
                  ]}
                  value={currentDensity}
                  onValueChange={handleDensityChange}
                  size="sm"
                  className="mr-8"
                />
              )}
              <TableColumns 
                table={tableInstance}
                trigger={
                  <button className=" text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-md transition-colors">
                    <CogIcon className="w-6 h-6" />
                  </button>
                }
              />
            </div>
          </div>

          {/* Bulk Actions */}
          {showBulkActions && hasSelectedRows && bulkActions.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-teal-50 border border-teal-200 rounded-lg">
              <span className="text-sm font-medium text-teal-900">
                {selectedRowsCount} row{selectedRowsCount !== 1 ? 's' : ''} selected:
              </span>
              <div className="flex items-center gap-2">
                {bulkActions.map((action) => {
                  const isDisabled = action.disabled?.(getSelectedRowsData()) || false
                  const getButtonClasses = () => {
                    const baseClasses = "flex items-center gap-1.5 px-3 py-1.5 text-sm rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    
                    switch (action.variant) {
                      case 'danger':
                        return `${baseClasses} bg-red-600 text-white hover:bg-red-700 disabled:hover:bg-red-600`
                      case 'secondary':
                        return `${baseClasses} bg-gray-600 text-white hover:bg-gray-700 disabled:hover:bg-gray-600`
                      case 'primary':
                      default:
                        return `${baseClasses} bg-teal-600 text-white hover:bg-teal-700 disabled:hover:bg-teal-600`
                    }
                  }

                  return (
                    <button
                      key={action.id}
                      onClick={() => handleBulkAction(action)}
                      disabled={isDisabled}
                      className={getButtonClasses()}
                    >
                      {action.icon}
                      {action.label}
                    </button>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <Table
        {...tableProps}
        density={currentDensity}
        onTableReady={handleTableReady}
        showColumnManagement={!showColumnManagementInHeader}
      />
    </div>
  )
}