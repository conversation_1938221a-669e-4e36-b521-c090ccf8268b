'use client'

import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline'
import { flexRender } from '@tanstack/react-table'
import { TableHeaderProps } from './types/table.types'
import { cn } from './utils/tableHelpers'

export const TableHeader = ({ table, className, density = 'normal' }: TableHeaderProps) => {
  const getPaddingClasses = () => {
    return density === 'condensed' ? 'p-1.5' : 'p-3'
  }

  const getTextClasses = () => {
    return density === 'condensed' ? 'text-xs' : 'text-sm'
  }
  return (
    <thead className={cn('sticky top-0 bg-white z-10 border-b border-slate-200', className)}>
      {table.getHeaderGroups().map((headerGroup) => (
        <tr key={headerGroup.id}>
          {headerGroup.headers.map((header) => {
            const canSort = header.column.getCanSort()
            const sortDirection = header.column.getIsSorted()
            const isResizing = header.column.getIsResizing()

            return (
              <th
                key={header.id}
                className={cn(
                  getPaddingClasses(),
                  'text-center font-semibold text-gray-700 whitespace-nowrap relative',
                  getTextClasses(),
                  canSort && 'cursor-pointer select-none hover:bg-gray-50',
                  isResizing && 'bg-gray-100'
                )}
                style={{
                  width: header.getSize(),
                }}
                onClick={header.column.getToggleSortingHandler()}
              >
                <div className="flex items-center justify-center gap-2">
                  {header.isPlaceholder ? null : (
                    <>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {canSort && (
                        <div className="flex flex-col">
                          <ChevronUpIcon 
                            className={cn(
                              'w-3 h-3',
                              sortDirection === 'asc' ? 'text-[#00B2A1]' : 'text-gray-300'
                            )}
                          />
                          <ChevronDownIcon 
                            className={cn(
                              'w-3 h-3 -mt-0.5',
                              sortDirection === 'desc' ? 'text-[#00B2A1]' : 'text-gray-300'
                            )}
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>
                
                {/* Column resizer */}
                {header.column.getCanResize() && (
                  <div
                    onMouseDown={header.getResizeHandler()}
                    onTouchStart={header.getResizeHandler()}
                    className={cn(
                      'absolute right-0 top-0 h-full w-1 cursor-col-resize select-none touch-none',
                      'hover:bg-[#00B2A1] opacity-0 hover:opacity-100',
                      isResizing && 'bg-[#00B2A1] opacity-100'
                    )}
                  />
                )}
              </th>
            )
          })}
        </tr>
      ))}
    </thead>
  )
}