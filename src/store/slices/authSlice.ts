import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authService } from '@/services/authService';

interface AuthState {
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  token: null,
  isAuthenticated: false,
  loading: false,
  error: null,
};

export const verifyAndSetToken = createAsyncThunk(
  'auth/verifyAndSetToken',
  async (token: string, { rejectWithValue }) => {
    try {
      const isValid = await authService.verifyToken(token);
      if (!isValid) {
        return rejectWithValue('Invalid token');
      }
      return token;
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Token verification failed';
      return rejectWithValue(message);
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearAuth: (state) => {
      state.token = null;
      state.isAuthenticated = false;
      state.error = null;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Verify and set token
      .addCase(verifyAndSetToken.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyAndSetToken.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(verifyAndSetToken.rejected, (state, action) => {
        state.loading = false;
        state.token = null;
        state.isAuthenticated = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearAuth, setError } = authSlice.actions;
export const authReducer = authSlice.reducer;