import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define search result interface
export interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: 'data_asset' | 'project' | 'document' | 'other';
  url?: string;
  metadata?: Record<string, unknown>;
  score?: number;
  createdAt?: string;
  updatedAt?: string;
}

// Define the search state interface
interface SearchState {
  currentQuery: string;
  searchHistory: string[];
  suggestions: string[];
  showSuggestions: boolean;
  isSearching: boolean;
  searchResults: SearchResult[];
  filters: {
    category?: string;
    dateRange?: {
      start: string;
      end: string;
    };
    sortBy?: 'relevance' | 'date' | 'name';
    sortOrder?: 'asc' | 'desc';
  };
  recentSearches: string[];
  maxHistoryItems: number;
}

// Define the initial state
const initialState: SearchState = {
  currentQuery: '',
  searchHistory: [],
  suggestions: [],
  showSuggestions: false,
  isSearching: false,
  searchResults: [],
  filters: {
    sortBy: 'relevance',
    sortOrder: 'desc',
  },
  recentSearches: [],
  maxHistoryItems: 10,
};

// Create the search slice
const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setCurrentQuery: (state, action: PayloadAction<string>) => {
      state.currentQuery = action.payload;
    },

    addToSearchHistory: (state, action: PayloadAction<string>) => {
      const query = action.payload.trim();
      if (query && !state.searchHistory.includes(query)) {
        state.searchHistory.unshift(query);
        // Keep only the most recent items
        if (state.searchHistory.length > state.maxHistoryItems) {
          state.searchHistory = state.searchHistory.slice(0, state.maxHistoryItems);
        }
      }
    },

    removeFromSearchHistory: (state, action: PayloadAction<string>) => {
      state.searchHistory = state.searchHistory.filter(
        (query) => query !== action.payload
      );
    },

    clearSearchHistory: (state) => {
      state.searchHistory = [];
    },

    setSuggestions: (state, action: PayloadAction<string[]>) => {
      state.suggestions = action.payload;
    },

    setShowSuggestions: (state, action: PayloadAction<boolean>) => {
      state.showSuggestions = action.payload;
    },

    setIsSearching: (state, action: PayloadAction<boolean>) => {
      state.isSearching = action.payload;
    },

    setSearchResults: (state, action: PayloadAction<SearchResult[]>) => {
      state.searchResults = action.payload;
    },

    updateFilters: (state, action: PayloadAction<Partial<SearchState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    clearFilters: (state) => {
      state.filters = {
        sortBy: 'relevance',
        sortOrder: 'desc',
      };
    },

    addToRecentSearches: (state, action: PayloadAction<string>) => {
      const query = action.payload.trim();
      if (query) {
        // Remove if already exists to avoid duplicates
        state.recentSearches = state.recentSearches.filter(
          (search) => search !== query
        );
        // Add to beginning
        state.recentSearches.unshift(query);
        // Keep only the most recent items
        if (state.recentSearches.length > state.maxHistoryItems) {
          state.recentSearches = state.recentSearches.slice(0, state.maxHistoryItems);
        }
      }
    },

    clearRecentSearches: (state) => {
      state.recentSearches = [];
    },

    clearSearch: (state) => {
      state.currentQuery = '';
      state.suggestions = [];
      state.showSuggestions = false;
      state.isSearching = false;
      state.searchResults = [];
    },
  },
});

// Export actions and reducer
export const {
  setCurrentQuery,
  addToSearchHistory,
  removeFromSearchHistory,
  clearSearchHistory,
  setSuggestions,
  setShowSuggestions,
  setIsSearching,
  setSearchResults,
  updateFilters,
  clearFilters,
  addToRecentSearches,
  clearRecentSearches,
  clearSearch,
} = searchSlice.actions;

export const searchReducer = searchSlice.reducer;