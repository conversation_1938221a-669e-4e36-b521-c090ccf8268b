# Sutra-Threads UI Component Library

## Project Overview

Sutra-Threads is a comprehensive design system and UI component library for building cohesive, accessible digital experiences. The project follows a dual-structure approach:

1. **Component Library** (`/src`) - The actual npm package source code
2. **Next.js Demo App** (`/sutra-ui-lib`) - A showcase/development environment for testing components

## Technology Stack

- **Framework**: Next.js 15.3.1 with React 19
- **Styling**: Tailwind CSS v4 with PostCSS
- **TypeScript**: Full TypeScript support with strict configuration
- **Icons**: Heroicons React
- **Build Tools**: Next.js Turbopack for development
- **Component Architecture**: Modern React patterns with TypeScript interfaces
- **Motion**: Framer Motion for animations (evident from build output)
- **Component Composition**: Radix UI Slot for flexible component composition

## Project Structure

```
/
├── src/                          # Main library source (package)
│   ├── blocks/                   # Block-level components
│   │   └── nav-primary/
│   ├── lib/                      # Utility functions
│   ├── styles/                   # Global styles
│   ├── templates/                # Template components
│   └── threads/                  # Core UI components
│       ├── button/
│       └── input/
├── sutra-ui-lib/                 # Next.js demo application
│   ├── src/app/                  # Next.js app directory
│   │   ├── components/           # Demo app components
│   │   │   ├── button.tsx
│   │   │   ├── checkbox.tsx
│   │   │   ├── checkbox-with-label.tsx
│   │   │   ├── custom-input.tsx
│   │   │   ├── submit-button.tsx
│   │   │   └── table/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── globals.css
│   └── public/                   # Static assets
├── dist/                         # Built library output
├── ref/                          # Design tokens and references
│   └── table-design-token.json   # Comprehensive design system tokens
└── readme.md                     # Project documentation
```

## Available Components

### Core Components (in demo app)
- **Button**: Customizable button with loading states, sizing options
- **Input**: Highly flexible input component with password visibility, info tooltips, validation
- **Checkbox**: Simple checkbox with custom styling
- **CheckboxWithLabel**: Checkbox with associated label
- **Table Components**: Advanced table structures (based on design tokens)

### Component Features
- TypeScript interfaces for all props
- Responsive design with Tailwind CSS
- Accessibility considerations built-in
- Loading states and error handling
- Customizable styling through props
- Design token integration

## Development Commands

### Demo Application (sutra-ui-lib)
```bash
cd sutra-ui-lib
npm run dev          # Start development server with Turbopack
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Component Library
The main library appears to have a build process that outputs to `/dist` with:
- TypeScript declarations (`.d.ts` files)
- ES modules (`.mjs`)
- CommonJS modules (`.cjs`)
- Source maps
- Bundled CSS

## Design System

### Design Tokens
The project includes a comprehensive design token system (`ref/table-design-token.json`) that defines:
- **Color System**: Primary brand color `#00B2A1` (teal/aqua)
- **Component Variants**: Different states (default, active, hover, disabled)
- **Spacing & Layout**: Consistent padding, margins, border radius
- **Typography**: Font sizes, weights, line heights
- **Accessibility**: Focus indicators, contrast ratios, minimum touch targets
- **Animation**: Transitions and motion design

### Key Design Principles
- **Coherence**: Components work harmoniously together
- **Flexibility**: Adaptable to different contexts
- **Accessibility**: WCAG compliant with proper contrast ratios (4.5:1 minimum)
- **Efficiency**: Optimized development workflows

### Styling Approach
- Tailwind CSS utility classes
- CSS custom properties for theming
- Inline styles for dynamic properties
- Design token integration for consistency

## Development Workflow

### Getting Started
1. Clone the repository
2. Install dependencies: `npm install` (both root and sutra-ui-lib)
3. Start demo app: `cd sutra-ui-lib && npm run dev`
4. Access at `http://localhost:3000`

### Component Development
- Components are developed in `/sutra-ui-lib/src/app/components/` for testing
- Production components should be built in `/src/threads/`
- Use the demo app for rapid prototyping and testing
- Follow TypeScript interfaces for prop definitions

### Build Process
- The library builds to `/dist` with multiple output formats
- Includes TypeScript declarations for consumers
- CSS is bundled and optimized
- Source maps provided for debugging

## Architecture Patterns

### Component Structure
```typescript
// Typical component interface
interface ComponentProps {
  // Required props
  value: string;
  onChange: (value: string) => void;
  label: string;
  
  // Optional props with defaults
  type?: 'text' | 'password' | 'email';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  
  // Styling props
  className?: string;
  width?: number | 'full';
  height?: number;
  // ... extensive styling customization
}
```

### State Management
- Local component state with React hooks
- No global state management (components are self-contained)
- Controlled components pattern for form inputs

### Styling Philosophy
- Utility-first approach with Tailwind CSS
- Design token integration for consistency
- Runtime style customization through props
- CSS custom properties for theming

## Current Git Status
- Branch: `next-setup`
- Main branch: `main`
- Recent commits focus on component development and documentation

## Notes for Future Development

1. **Dual Structure**: Remember this project has both a library and demo app
2. **Design Tokens**: Leverage the comprehensive token system in `ref/`
3. **TypeScript**: Maintain strict typing for all components
4. **Accessibility**: Follow established patterns for focus management and ARIA
5. **Testing**: Consider adding tests for components in both environments
6. **Documentation**: Use the demo app to showcase component usage

## Key Files to Reference

- `/sutra-ui-lib/src/app/components/custom-input.tsx` - Complex component example
- `/ref/table-design-token.json` - Complete design system reference
- `/sutra-ui-lib/src/app/page.tsx` - Component usage examples
- `/dist/index.d.ts` - Library export structure

This project represents a mature design system with thoughtful architecture for both development and consumption.